"""
Benchmark test functions for IAF-FBO algorithm
"""

import numpy as np


class BenchmarkFunction:
    """Base class for benchmark functions"""
    
    def __init__(self, dimension, bounds=None, rotation_matrix=None, shift_vector=None):
        """
        Initialize benchmark function
        
        Args:
            dimension: Problem dimension
            bounds: Problem bounds (lower, upper)
            rotation_matrix: Rotation matrix for transformation
            shift_vector: Shift vector for global optimum
        """
        self.dimension = dimension
        self.bounds = bounds if bounds is not None else (-5.0, 5.0)
        self.rotation_matrix = rotation_matrix
        self.shift_vector = shift_vector
        
    def __call__(self, x):
        """Evaluate function at point x"""
        return self.evaluate(x)
    
    def evaluate(self, x):
        """Evaluate function - to be implemented by subclasses"""
        raise NotImplementedError
    
    def transform(self, x):
        """Apply rotation and shift transformations"""
        x = np.asarray(x)
        
        # Apply shift
        if self.shift_vector is not None:
            x = x - self.shift_vector
        
        # Apply rotation
        if self.rotation_matrix is not None:
            x = self.rotation_matrix @ x
        
        return x


class SphereFunction(BenchmarkFunction):
    """Sphere function: f(x) = sum(x_i^2)"""
    
    def __init__(self, dimension, bounds=(-100, 100), rotation_matrix=None, shift_vector=None):
        super().__init__(dimension, bounds, rotation_matrix, shift_vector)
        self.name = "Sphere"
        self.global_optimum = 0.0
    
    def evaluate(self, x):
        x = self.transform(x)
        return np.sum(x**2)


class RastriginFunction(BenchmarkFunction):
    """Rastrigin function: f(x) = A*n + sum(x_i^2 - A*cos(2*pi*x_i))"""
    
    def __init__(self, dimension, bounds=(-50, 50), rotation_matrix=None, shift_vector=None):
        super().__init__(dimension, bounds, rotation_matrix, shift_vector)
        self.name = "Rastrigin"
        self.global_optimum = 0.0
        self.A = 10
    
    def evaluate(self, x):
        x = self.transform(x)
        n = len(x)
        return self.A * n + np.sum(x**2 - self.A * np.cos(2 * np.pi * x))


class AckleyFunction(BenchmarkFunction):
    """Ackley function"""
    
    def __init__(self, dimension, bounds=(-50, 50), rotation_matrix=None, shift_vector=None):
        super().__init__(dimension, bounds, rotation_matrix, shift_vector)
        self.name = "Ackley"
        self.global_optimum = 0.0
    
    def evaluate(self, x):
        x = self.transform(x)
        a, b, c = 20, 0.2, 2*np.pi
        d = len(x)
        sum1 = np.sum(x**2)
        sum2 = np.sum(np.cos(c * x))
        return -a * np.exp(-b * np.sqrt(sum1/d)) - np.exp(sum2/d) + a + np.e


class GriewankFunction(BenchmarkFunction):
    """Griewank function"""
    
    def __init__(self, dimension, bounds=(-100, 100), rotation_matrix=None, shift_vector=None):
        super().__init__(dimension, bounds, rotation_matrix, shift_vector)
        self.name = "Griewank"
        self.global_optimum = 0.0
    
    def evaluate(self, x):
        x = self.transform(x)
        sum_term = np.sum(x**2) / 4000
        prod_term = np.prod(np.cos(x / np.sqrt(np.arange(1, len(x) + 1))))
        return sum_term - prod_term + 1


class RosenbrockFunction(BenchmarkFunction):
    """Rosenbrock function"""
    
    def __init__(self, dimension, bounds=(-50, 50), rotation_matrix=None, shift_vector=None):
        super().__init__(dimension, bounds, rotation_matrix, shift_vector)
        self.name = "Rosenbrock"
        self.global_optimum = 0.0
    
    def evaluate(self, x):
        x = self.transform(x)
        result = 0
        for i in range(len(x) - 1):
            result += 100 * (x[i+1] - x[i]**2)**2 + (1 - x[i])**2
        return result


class SchwefelFunction(BenchmarkFunction):
    """Schwefel function"""
    
    def __init__(self, dimension, bounds=(-500, 500), rotation_matrix=None, shift_vector=None):
        super().__init__(dimension, bounds, rotation_matrix, shift_vector)
        self.name = "Schwefel"
        self.global_optimum = 0.0
    
    def evaluate(self, x):
        x = self.transform(x)
        return 418.9829 * len(x) - np.sum(x * np.sin(np.sqrt(np.abs(x))))


class WeierstrassFunction(BenchmarkFunction):
    """Weierstrass function"""
    
    def __init__(self, dimension, bounds=(-0.5, 0.5), rotation_matrix=None, shift_vector=None):
        super().__init__(dimension, bounds, rotation_matrix, shift_vector)
        self.name = "Weierstrass"
        self.global_optimum = 0.0
        self.a = 0.5
        self.b = 3
        self.kmax = 20
    
    def evaluate(self, x):
        x = self.transform(x)
        n = len(x)
        
        # First sum
        sum1 = 0
        for i in range(n):
            for k in range(self.kmax + 1):
                sum1 += (self.a**k) * np.cos(2 * np.pi * (self.b**k) * (x[i] + 0.5))
        
        # Second sum
        sum2 = 0
        for k in range(self.kmax + 1):
            sum2 += (self.a**k) * np.cos(2 * np.pi * (self.b**k) * 0.5)
        
        return sum1 - n * sum2


# Benchmark function factory
BENCHMARK_FUNCTIONS = {
    'sphere': SphereFunction,
    'rastrigin': RastriginFunction,
    'ackley': AckleyFunction,
    'griewank': GriewankFunction,
    'rosenbrock': RosenbrockFunction,
    'schwefel': SchwefelFunction,
    'weierstrass': WeierstrassFunction,
}


def create_benchmark_function(name, dimension, bounds=None, rotation_matrix=None, shift_vector=None):
    """
    Create a benchmark function
    
    Args:
        name: Function name
        dimension: Problem dimension
        bounds: Problem bounds
        rotation_matrix: Rotation matrix for transformation
        shift_vector: Shift vector for global optimum
    
    Returns:
        Benchmark function instance
    """
    if name.lower() not in BENCHMARK_FUNCTIONS:
        raise ValueError(f"Unknown benchmark function: {name}")
    
    func_class = BENCHMARK_FUNCTIONS[name.lower()]
    return func_class(dimension, bounds, rotation_matrix, shift_vector)


def generate_rotation_matrix(dimension, random_state=None):
    """
    Generate a random rotation matrix
    
    Args:
        dimension: Matrix dimension
        random_state: Random seed
    
    Returns:
        Orthogonal rotation matrix
    """
    if random_state is not None:
        np.random.seed(random_state)
    
    # Generate random matrix
    A = np.random.randn(dimension, dimension)
    
    # QR decomposition to get orthogonal matrix
    Q, R = np.linalg.qr(A)
    
    # Ensure proper rotation (det = 1)
    if np.linalg.det(Q) < 0:
        Q[:, 0] *= -1
    
    return Q


def generate_shift_vector(dimension, bounds, random_state=None):
    """
    Generate a random shift vector within bounds
    
    Args:
        dimension: Vector dimension
        bounds: Problem bounds (lower, upper)
        random_state: Random seed
    
    Returns:
        Shift vector
    """
    if random_state is not None:
        np.random.seed(random_state)
    
    if isinstance(bounds, tuple):
        lower, upper = bounds
        return np.random.uniform(lower, upper, dimension)
    else:
        # Assume bounds is already an array
        return np.random.uniform(-1, 1, dimension) * np.abs(bounds)


def create_multitask_benchmark(function_names, dimension, n_tasks, 
                              bounds=None, use_rotation=True, use_shift=True,
                              random_state=None):
    """
    Create multiple benchmark functions for multi-task optimization
    
    Args:
        function_names: List of function names or single name
        dimension: Problem dimension
        n_tasks: Number of tasks
        bounds: Problem bounds
        use_rotation: Whether to apply random rotations
        use_shift: Whether to apply random shifts
        random_state: Random seed
    
    Returns:
        List of benchmark function instances
    """
    if isinstance(function_names, str):
        function_names = [function_names] * n_tasks
    elif len(function_names) < n_tasks:
        # Repeat function names to match n_tasks
        function_names = (function_names * (n_tasks // len(function_names) + 1))[:n_tasks]
    
    functions = []
    
    for i in range(n_tasks):
        # Generate rotation matrix
        rotation_matrix = None
        if use_rotation:
            rotation_matrix = generate_rotation_matrix(
                dimension, 
                random_state=random_state + i if random_state is not None else None
            )
        
        # Generate shift vector
        shift_vector = None
        if use_shift:
            func_bounds = bounds if bounds is not None else (-5, 5)
            shift_vector = generate_shift_vector(
                dimension, 
                func_bounds,
                random_state=random_state + i + 1000 if random_state is not None else None
            )
        
        # Create function
        func = create_benchmark_function(
            function_names[i], 
            dimension, 
            bounds, 
            rotation_matrix, 
            shift_vector
        )
        
        functions.append(func)
    
    return functions


# Predefined benchmark suites
def get_cec_like_suite(dimension, random_state=None):
    """Get a CEC-like benchmark suite"""
    function_names = ['sphere', 'rastrigin', 'ackley', 'griewank', 'rosenbrock', 'schwefel']
    bounds_list = [(-100, 100), (-50, 50), (-50, 50), (-100, 100), (-50, 50), (-500, 500)]
    
    functions = []
    for i, (name, bounds) in enumerate(zip(function_names, bounds_list)):
        func = create_benchmark_function(
            name, dimension, bounds,
            rotation_matrix=generate_rotation_matrix(dimension, random_state + i if random_state else None),
            shift_vector=generate_shift_vector(dimension, bounds, random_state + i + 100 if random_state else None)
        )
        functions.append(func)
    
    return functions
