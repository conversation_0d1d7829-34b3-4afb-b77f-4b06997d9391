function [ T ] = Griewank_( X, M,opt )
%ROSENBROCK20 Summary of this function goes here
% %%%%%Shifte<PERSON><PERSON>’s Function [-100 100] F6

T = [];

for t = 1:size(X,1)
    x = X(t,:);
    var = x;
    dim = length(var);
    var = (M*(var-opt)')';  %
    sum1 = 0; sum2 = 1;
    for i = 1: dim
        sum1 = sum1 + var(i)*var(i);
        sum2 = sum2 * cos(var(i)/(sqrt(i)));
    end

    obj = 1+1/4000*sum1-sum2;
    T(t,:) = obj;
end
end

