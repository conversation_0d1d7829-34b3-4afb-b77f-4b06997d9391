function H = lhsclassic(samples,n)
    % Generate the intervals
    cut = linspace(0, 1, samples + 1);
    
    % Fill points uniformly in each interval
    u = rand(samples, n);
    a = cut(1:samples);
    b = cut(2:samples + 1);
    rdpoints = zeros(samples, n);
    for j = 1:n
        rdpoints(:, j) = u(:, j).*(b-a)' + a';
    end
    
    % Make the random pairings
    H = zeros(samples, n);
    for j = 1:n
        order = randperm(samples);
        H(:, j) = rdpoints(order, j);
    end
end