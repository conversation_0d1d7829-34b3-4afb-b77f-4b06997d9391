"""
Data partitioning strategies for federated optimization
Controls IID vs Non-IID data distribution across clients
"""

import numpy as np


def create_iid_partitions(bounds, n_clients):
    """
    Create IID partitions where all clients use the same search space
    
    Args:
        bounds: Global problem bounds (lower, upper)
        n_clients: Number of clients
    
    Returns:
        List of bounds for each client (all identical)
    """
    client_bounds = []
    for _ in range(n_clients):
        client_bounds.append(bounds)
    
    return client_bounds


def create_non_iid_partitions(bounds, n_clients, np_per_dim=2, overlap_ratio=0.1, random_state=None):
    """
    Create Non-IID partitions where clients have different search spaces
    
    Args:
        bounds: Global problem bounds (lower, upper)
        n_clients: Number of clients
        np_per_dim: Number of partitions per dimension
        overlap_ratio: Ratio of overlap between adjacent partitions
        random_state: Random seed for reproducibility
    
    Returns:
        List of bounds for each client
    """
    if random_state is not None:
        np.random.seed(random_state)
    
    lower_bounds, upper_bounds = bounds
    dimension = len(lower_bounds)
    
    # Calculate partition assignments for each client
    client_bounds = []
    
    for client_id in range(n_clients):
        client_lower = np.zeros(dimension)
        client_upper = np.zeros(dimension)
        
        for dim in range(dimension):
            # Determine which partition this client gets for this dimension
            partition_id = client_id % np_per_dim
            
            # Calculate partition boundaries
            dim_range = upper_bounds[dim] - lower_bounds[dim]
            partition_size = dim_range / np_per_dim
            
            # Base partition boundaries
            partition_lower = lower_bounds[dim] + partition_id * partition_size
            partition_upper = lower_bounds[dim] + (partition_id + 1) * partition_size
            
            # Add overlap
            overlap_size = partition_size * overlap_ratio
            
            # Extend boundaries with overlap
            extended_lower = partition_lower - overlap_size
            extended_upper = partition_upper + overlap_size
            
            # Ensure boundaries don't exceed global bounds
            client_lower[dim] = max(extended_lower, lower_bounds[dim])
            client_upper[dim] = min(extended_upper, upper_bounds[dim])
        
        client_bounds.append((client_lower, client_upper))
    
    return client_bounds


def create_random_partitions(bounds, n_clients, partition_ratio=0.5, random_state=None):
    """
    Create random partitions where each client gets a random subset of the search space
    
    Args:
        bounds: Global problem bounds (lower, upper)
        n_clients: Number of clients
        partition_ratio: Ratio of the global space each client gets
        random_state: Random seed
    
    Returns:
        List of bounds for each client
    """
    if random_state is not None:
        np.random.seed(random_state)
    
    lower_bounds, upper_bounds = bounds
    dimension = len(lower_bounds)
    
    client_bounds = []
    
    for client_id in range(n_clients):
        client_lower = np.zeros(dimension)
        client_upper = np.zeros(dimension)
        
        for dim in range(dimension):
            # Calculate the size of this client's partition
            dim_range = upper_bounds[dim] - lower_bounds[dim]
            client_range = dim_range * partition_ratio
            
            # Randomly place the partition within the global bounds
            max_start = upper_bounds[dim] - client_range
            start_pos = np.random.uniform(lower_bounds[dim], max_start)
            
            client_lower[dim] = start_pos
            client_upper[dim] = start_pos + client_range
        
        client_bounds.append((client_lower, client_upper))
    
    return client_bounds


def create_clustered_partitions(bounds, n_clients, n_clusters=2, cluster_ratio=0.6, random_state=None):
    """
    Create clustered partitions where clients are grouped into clusters with similar search spaces
    
    Args:
        bounds: Global problem bounds (lower, upper)
        n_clients: Number of clients
        n_clusters: Number of clusters
        cluster_ratio: Ratio of space each cluster covers
        random_state: Random seed
    
    Returns:
        List of bounds for each client
    """
    if random_state is not None:
        np.random.seed(random_state)
    
    lower_bounds, upper_bounds = bounds
    dimension = len(lower_bounds)
    
    # Assign clients to clusters
    clients_per_cluster = n_clients // n_clusters
    remaining_clients = n_clients % n_clusters
    
    client_cluster_assignment = []
    for cluster_id in range(n_clusters):
        cluster_size = clients_per_cluster + (1 if cluster_id < remaining_clients else 0)
        client_cluster_assignment.extend([cluster_id] * cluster_size)
    
    # Create cluster centers
    cluster_centers = []
    for cluster_id in range(n_clusters):
        center = np.random.uniform(lower_bounds, upper_bounds)
        cluster_centers.append(center)
    
    # Create client bounds based on cluster assignment
    client_bounds = []
    
    for client_id in range(n_clients):
        cluster_id = client_cluster_assignment[client_id]
        cluster_center = cluster_centers[cluster_id]
        
        client_lower = np.zeros(dimension)
        client_upper = np.zeros(dimension)
        
        for dim in range(dimension):
            # Calculate cluster bounds
            dim_range = upper_bounds[dim] - lower_bounds[dim]
            cluster_range = dim_range * cluster_ratio
            
            # Center the cluster around the cluster center
            cluster_lower = max(cluster_center[dim] - cluster_range/2, lower_bounds[dim])
            cluster_upper = min(cluster_center[dim] + cluster_range/2, upper_bounds[dim])
            
            # Add some randomness within the cluster
            intra_cluster_noise = cluster_range * 0.1
            client_lower[dim] = max(cluster_lower - intra_cluster_noise, lower_bounds[dim])
            client_upper[dim] = min(cluster_upper + intra_cluster_noise, upper_bounds[dim])
        
        client_bounds.append((client_lower, client_upper))
    
    return client_bounds


def create_hierarchical_partitions(bounds, n_clients, hierarchy_levels=2, random_state=None):
    """
    Create hierarchical partitions with multiple levels of subdivision
    
    Args:
        bounds: Global problem bounds (lower, upper)
        n_clients: Number of clients
        hierarchy_levels: Number of hierarchy levels
        random_state: Random seed
    
    Returns:
        List of bounds for each client
    """
    if random_state is not None:
        np.random.seed(random_state)
    
    lower_bounds, upper_bounds = bounds
    dimension = len(lower_bounds)
    
    # Calculate partitions per level
    partitions_per_level = int(np.ceil(n_clients ** (1.0 / hierarchy_levels)))
    
    client_bounds = []
    
    for client_id in range(n_clients):
        client_lower = lower_bounds.copy()
        client_upper = upper_bounds.copy()
        
        # Apply hierarchical partitioning
        temp_client_id = client_id
        
        for level in range(hierarchy_levels):
            # Determine partition at this level
            partition_id = temp_client_id % partitions_per_level
            temp_client_id //= partitions_per_level
            
            # Apply partitioning to a random dimension
            dim = level % dimension
            
            # Calculate partition boundaries for this level
            current_range = client_upper[dim] - client_lower[dim]
            partition_size = current_range / partitions_per_level
            
            new_lower = client_lower[dim] + partition_id * partition_size
            new_upper = client_lower[dim] + (partition_id + 1) * partition_size
            
            client_lower[dim] = new_lower
            client_upper[dim] = new_upper
        
        client_bounds.append((client_lower, client_upper))
    
    return client_bounds


def visualize_partitions(client_bounds, dimension_pair=(0, 1), title="Client Partitions"):
    """
    Visualize 2D projections of client partitions
    
    Args:
        client_bounds: List of client bounds
        dimension_pair: Tuple of dimensions to visualize
        title: Plot title
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        colors = plt.cm.Set3(np.linspace(0, 1, len(client_bounds)))
        
        for i, (lower, upper) in enumerate(client_bounds):
            dim1, dim2 = dimension_pair
            
            # Create rectangle for this client's partition
            width = upper[dim1] - lower[dim1]
            height = upper[dim2] - lower[dim2]
            
            rect = patches.Rectangle(
                (lower[dim1], lower[dim2]), width, height,
                linewidth=2, edgecolor=colors[i], facecolor=colors[i], alpha=0.3,
                label=f'Client {i}'
            )
            ax.add_patch(rect)
        
        ax.set_xlabel(f'Dimension {dimension_pair[0]}')
        ax.set_ylabel(f'Dimension {dimension_pair[1]}')
        ax.set_title(title)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
    except ImportError:
        print("Matplotlib not available. Cannot visualize partitions.")


# Partition strategy factory
PARTITION_STRATEGIES = {
    'iid': create_iid_partitions,
    'non_iid': create_non_iid_partitions,
    'random': create_random_partitions,
    'clustered': create_clustered_partitions,
    'hierarchical': create_hierarchical_partitions,
}


def create_partitions(strategy, bounds, n_clients, **kwargs):
    """
    Create client partitions using specified strategy
    
    Args:
        strategy: Partitioning strategy name
        bounds: Global problem bounds
        n_clients: Number of clients
        **kwargs: Additional strategy-specific parameters
    
    Returns:
        List of client bounds
    """
    if strategy not in PARTITION_STRATEGIES:
        raise ValueError(f"Unknown partitioning strategy: {strategy}")
    
    return PARTITION_STRATEGIES[strategy](bounds, n_clients, **kwargs)


def get_partition_info(client_bounds):
    """
    Get information about the partitions
    
    Args:
        client_bounds: List of client bounds
    
    Returns:
        Dictionary with partition statistics
    """
    n_clients = len(client_bounds)
    dimension = len(client_bounds[0][0])
    
    # Calculate volumes
    volumes = []
    for lower, upper in client_bounds:
        volume = np.prod(upper - lower)
        volumes.append(volume)
    
    # Calculate overlaps (simplified - just check if any bounds overlap)
    overlaps = 0
    for i in range(n_clients):
        for j in range(i + 1, n_clients):
            lower1, upper1 = client_bounds[i]
            lower2, upper2 = client_bounds[j]
            
            # Check if there's overlap in all dimensions
            overlap_in_all_dims = True
            for dim in range(dimension):
                if upper1[dim] <= lower2[dim] or upper2[dim] <= lower1[dim]:
                    overlap_in_all_dims = False
                    break
            
            if overlap_in_all_dims:
                overlaps += 1
    
    return {
        'n_clients': n_clients,
        'dimension': dimension,
        'volumes': volumes,
        'mean_volume': np.mean(volumes),
        'std_volume': np.std(volumes),
        'total_overlaps': overlaps,
        'overlap_ratio': overlaps / (n_clients * (n_clients - 1) / 2)
    }
