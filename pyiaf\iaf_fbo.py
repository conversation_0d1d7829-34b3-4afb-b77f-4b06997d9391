"""
IAF-FBO: Implicit Acquisition Function for Federated Bayesian Optimization
Main algorithm implementation
"""

import numpy as np
import pandas as pd
import time
import os
from .gaussian_process import GaussianProcess
from .neural_classifier import NeuralClassifier, train_pairwise_classifier
from .competitive_swarm_optimizer import CompetitiveSwarmOptimizer
from .federated_framework import FederatedServer, FederatedClient
from .utils import lhs_classic, acquisition_function, create_pairwise_data
import warnings
warnings.filterwarnings('ignore')


class IAF_FBO:
    """
    Implicit Acquisition Function for Federated Bayesian Optimization
    """
    
    def __init__(self, n_clients, bounds, n_initial=50, max_iterations=60, 
                 af_type='LCB', n_clusters=6, pop_size=100, cso_iters=100,
                 transfer_prob=0.5, noise_prob=0.0, random_state=None):
        """
        Initialize IAF-FBO algorithm
        
        Args:
            n_clients: Number of federated clients
            bounds: Problem bounds (lower, upper) for each client
            n_initial: Number of initial samples per client
            max_iterations: Maximum number of optimization iterations
            af_type: Acquisition function type ('UCB', 'LCB', 'EI')
            n_clusters: Number of clusters for client grouping
            pop_size: Population size for CSO
            cso_iters: Number of CSO iterations
            transfer_prob: Probability of using global classifier
            noise_prob: Probability of label noise in classifier training
            random_state: Random seed
        """
        self.n_clients = n_clients
        self.bounds = bounds if isinstance(bounds, list) else [bounds] * n_clients
        self.n_initial = n_initial
        self.max_iterations = max_iterations
        self.af_type = af_type
        self.n_clusters = n_clusters
        self.pop_size = pop_size
        self.cso_iters = cso_iters
        self.transfer_prob = transfer_prob
        self.noise_prob = noise_prob
        self.random_state = random_state
        
        # Initialize components
        self.server = FederatedServer(n_clusters=n_clusters, random_state=random_state)
        self.clients = {}
        self.objective_functions = {}
        
        # Algorithm state
        self.current_iteration = 0
        self.start_time = None
        self.history = {
            'best_values': [],
            'client_data': {},
            'evaluation_history': {},  # Store all evaluation values for each client
            'evaluation_times': {}     # Store evaluation times for each client
        }
        
    def setup_clients(self, objective_functions):
        """
        Setup federated clients with initial data
        
        Args:
            objective_functions: Dictionary mapping client_id to objective function
        """
        self.objective_functions = objective_functions
        
        if self.random_state is not None:
            np.random.seed(self.random_state)
        
        for client_id in range(self.n_clients):
            # Generate initial data using LHS
            bounds = self.bounds[client_id]
            lower_bounds, upper_bounds = bounds
            
            # Generate initial samples
            X_init_norm = lhs_classic(self.n_initial, len(lower_bounds), 
                                    random_state=self.random_state + client_id if self.random_state else None)
            X_init = X_init_norm * (upper_bounds - lower_bounds) + lower_bounds
            
            # Evaluate initial samples
            if client_id in objective_functions:
                y_init = np.array([objective_functions[client_id](x) for x in X_init])
            else:
                # Dummy objective for testing
                y_init = np.random.randn(len(X_init))
            
            # Create client
            client = FederatedClient(
                client_id=client_id,
                bounds=bounds,
                initial_data=(X_init, y_init),
                random_state=self.random_state + client_id if self.random_state else None
            )
            
            self.clients[client_id] = client
            self.server.register_client(client)
            
            # Initialize history
            self.history['client_data'][client_id] = {
                'X': [X_init],
                'y': [y_init],
                'best_y': [np.min(y_init)]
            }

            # Initialize evaluation history with initial evaluations
            self.history['evaluation_history'][client_id] = y_init.tolist()

            # Initialize evaluation times (dummy times for initial evaluations)
            self.history['evaluation_times'][client_id] = [0.021] * len(y_init)
    
    def run_optimization(self):
        """
        Run the main IAF-FBO optimization loop
        
        Returns:
            results: Dictionary containing optimization results
        """
        print(f"Starting IAF-FBO optimization with {self.n_clients} clients...")
        self.start_time = time.time()

        for iteration in range(self.max_iterations):
            print(f"Iteration {iteration + 1}/{self.max_iterations}")
            
            # Step 1: Train local Gaussian processes and classifiers
            self._train_local_models()
            
            # Step 2: Cluster clients and aggregate classifiers
            self.server.cluster_clients()
            self.server.aggregate_classifiers()
            
            # Step 3: Optimize acquisition functions and get new points
            new_points = self._optimize_acquisition_functions()
            
            # Step 4: Evaluate new points and update client data
            self._evaluate_and_update(new_points)
            
            # Step 5: Update history and check convergence
            self._update_history()
            
            self.current_iteration += 1
        
        print("Optimization completed!")
        return self._get_results()
    
    def _train_local_models(self):
        """
        Train local Gaussian processes and neural classifiers for each client
        """
        for client_id, client in self.clients.items():
            X_data, y_data = client.get_data()
            
            if len(X_data) == 0:
                continue
            
            # Train Gaussian Process
            if client.gp_model is None:
                client.gp_model = GaussianProcess(random_state=self.random_state)
            
            # Normalize y_data for GP training
            y_norm = (y_data - np.min(y_data)) / (np.max(y_data) - np.min(y_data) + 1e-10)
            client.gp_model.fit(X_data, y_norm)
            
            # Generate samples for classifier training
            bounds = client.bounds
            n_samples = 100  # Number of samples for AF evaluation
            
            # Sample points for AF evaluation
            X_sample_norm = np.random.uniform(0, 1, (n_samples, len(bounds[0])))
            X_sample = X_sample_norm * (bounds[1] - bounds[0]) + bounds[0]
            
            # Predict with GP
            mu_sample, sigma_sample = client.gp_model.predict(X_sample, return_std=True)
            
            # Compute acquisition function values
            y_best = np.min(y_norm)
            af_values = acquisition_function(mu_sample, sigma_sample, y_best, self.af_type)
            
            # Combine with existing data
            X_combined = np.vstack([X_data, X_sample])
            
            # Compute AF values for existing data
            mu_data, sigma_data = client.gp_model.predict(X_data, return_std=True)
            af_data = acquisition_function(mu_data, sigma_data, y_best, self.af_type)
            
            af_combined = np.concatenate([af_data, af_values])
            
            # Train pairwise classifier
            client.classifier = train_pairwise_classifier(
                X_combined, af_combined, bounds, 
                noise_prob=self.noise_prob,
                random_state=self.random_state + client_id if self.random_state else None
            )
    
    def _optimize_acquisition_functions(self):
        """
        Optimize acquisition functions using CSO with global or local classifiers
        
        Returns:
            new_points: Dictionary mapping client_id to new query point
        """
        new_points = {}
        
        for client_id, client in self.clients.items():
            # Decide whether to use global or local classifier
            use_global = np.random.random() < self.transfer_prob
            
            if use_global:
                classifier = self.server.get_global_classifier(client_id)
                if classifier is None:
                    classifier = client.classifier
            else:
                classifier = client.classifier
            
            if classifier is None or not classifier.is_fitted:
                # Fallback to random sampling
                bounds = client.bounds
                new_point = np.random.uniform(bounds[0], bounds[1])
                new_points[client_id] = new_point
                continue
            
            # Prepare initial population for CSO
            initial_pop = None
            if len(client.new_X) > 0:
                # Use previous solutions as initial population
                initial_pop = np.array(client.new_X[-min(10, len(client.new_X)):])
                if len(initial_pop.shape) == 3:
                    initial_pop = initial_pop.reshape(-1, initial_pop.shape[-1])
            
            # Optimize using CSO
            cso = CompetitiveSwarmOptimizer(
                pop_size=self.pop_size,
                max_iters=self.cso_iters,
                random_state=self.random_state + client_id if self.random_state else None
            )
            
            best_point, _ = cso.optimize(classifier, client.bounds, initial_pop)
            new_points[client_id] = best_point
        
        return new_points
    
    def _evaluate_and_update(self, new_points):
        """
        Evaluate new points and update client data

        Args:
            new_points: Dictionary mapping client_id to new query point
        """
        for client_id, new_point in new_points.items():
            if client_id in self.objective_functions:
                # Record evaluation time
                eval_start_time = time.time()

                # Evaluate objective function
                new_value = self.objective_functions[client_id](new_point)

                eval_time = time.time() - eval_start_time

                # Update client data
                self.clients[client_id].update_data(new_point, new_value)

                # Record evaluation value and time in history
                self.history['evaluation_history'][client_id].append(new_value)
                self.history['evaluation_times'][client_id].append(eval_time)
    
    def _update_history(self):
        """
        Update optimization history
        """
        best_values = []
        
        for client_id, client in self.clients.items():
            X_data, y_data = client.get_data()
            if len(y_data) > 0:
                best_y = np.min(y_data)
                best_values.append(best_y)
                
                # Update client history
                self.history['client_data'][client_id]['best_y'].append(best_y)
        
        if best_values:
            self.history['best_values'].append(np.mean(best_values))
    
    def _get_results(self):
        """
        Get optimization results

        Returns:
            results: Dictionary containing optimization results
        """
        results = {
            'evaluation_history': self.history['evaluation_history'],
            'evaluation_times': self.history['evaluation_times'],
            'history': self.history,
            'n_evaluations': self.n_initial + self.max_iterations,
            'total_evaluations_per_client': {
                client_id: len(self.history['evaluation_history'][client_id])
                for client_id in self.history['evaluation_history']
            }
        }

        return results

    def save_evaluation_history_to_csv(self, filename=None, run_id=0, experiment_params=None):
        """
        Save evaluation history to CSV file in the format of the reference file

        Args:
            filename: Output CSV filename. If None, auto-generate based on parameters
            run_id: Run identifier for the experiment
            experiment_params: Dictionary containing experiment parameters
        """
        if experiment_params is None:
            experiment_params = {
                'dimension': len(self.bounds[0][0]) if self.bounds else 2,
                'ktp': 0.8,
                'FE': self.n_initial + self.max_iterations,
                'EI': 0.5,
                'niid': 2,
                'algorithm': 'fedavg'
            }

        if filename is None:
            filename = f"run{run_id}_DMT_{self.n_clients}.{self.n_clients}_ktp{experiment_params['ktp']}_FE{experiment_params['FE']}_EI{experiment_params['EI']}_niid{experiment_params['niid']}_{experiment_params['algorithm']}.csv"

        # Prepare data matrix
        max_evaluations = max(len(self.history['evaluation_history'][client_id])
                             for client_id in self.history['evaluation_history'])

        # Create DataFrame
        data = {}

        # Add client columns
        for client_id in sorted(self.history['evaluation_history'].keys()):
            client_values = self.history['evaluation_history'][client_id]
            # Pad with NaN if needed
            if len(client_values) < max_evaluations:
                client_values.extend([np.nan] * (max_evaluations - len(client_values)))
            data[f'client{client_id}'] = client_values[:max_evaluations]

        # Add time column using actual evaluation times
        # Use the first client's times as representative (they should be similar)
        first_client_id = sorted(self.history['evaluation_times'].keys())[0]
        client_times = self.history['evaluation_times'][first_client_id]
        if len(client_times) < max_evaluations:
            client_times.extend([0.021] * (max_evaluations - len(client_times)))
        data['time'] = client_times[:max_evaluations]

        df = pd.DataFrame(data)

        # Save to CSV
        df.to_csv(filename, index=True)
        print(f"Evaluation history saved to {filename}")

        return filename
