function [ T ] = Ackley_( X, M,opt )
%ROSENBROCK20 Summary of this function goes here
% %%%%%Shift<PERSON><PERSON><PERSON>’s Function [-100 100] F6
T = [];


for t = 1:size(X,1)
    x = X(t,:);
    var = x;
    dim = length(var);
    var = (M*(var-opt)')';
    sum1 = 0; sum2 = 0;
    for i = 1: dim
        sum1 = sum1 + var(i)*var(i);
        sum2 = sum2 + cos(2*pi*var(i));
    end
    avgsum1 = sum1/dim;
    avgsum2 = sum2/dim;

    obj = -20*exp(-0.2*sqrt(avgsum1)) - exp(avgsum2) + 20 + exp(1);
    T(t,:) = obj;

end
end

